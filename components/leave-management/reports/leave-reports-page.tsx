"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>hart, FileText, Loader2 } from "lucide-react"
import { EmptyState } from "@/components/empty-state"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangePicker } from "@/components/date-range-picker"
import { useLeaveReports } from "@/hooks/use-leave-reports"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"

interface LeaveReportsPageProps {
  userId: string
}

interface Department {
  id: string;
  name: string;
  code: string;
  employeeCount: number;
}

export function LeaveReportsPage({ userId }: LeaveReportsPageProps) {
  const [timeRange, setTimeRange] = useState<string>("month")
  const [department, setDepartment] = useState<string>("all")
  const [departments, setDepartments] = useState<Department[]>([])
  const [overviewData, setOverviewData] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<string>("overview")

  const {
    loading,
    error,
    generateOverviewReport,
    getDepartments,
    exportReport
  } = useLeaveReports()

  // Fetch departments on component mount
  useEffect(() => {
    const fetchDepartments = async () => {
      const depts = await getDepartments()
      setDepartments(depts)
    }
    fetchDepartments()
  }, [getDepartments])

  // Fetch overview data when filters change
  useEffect(() => {
    const fetchOverviewData = async () => {
      const data = await generateOverviewReport(
        timeRange,
        department === "all" ? undefined : department
      )
      setOverviewData(data)
    }
    fetchOverviewData()
  }, [timeRange, department, generateOverviewReport])

  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    const params = {
      timeRange,
      ...(department !== "all" && { departmentId: department })
    }

    await exportReport(activeTab, params, format)
  }

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat().format(num)
  }

  const formatPercentage = (num: number): string => {
    const sign = num > 0 ? '+' : ''
    return `${sign}${num.toFixed(1)}%`
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={department} onValueChange={setDepartment}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.id}>
                  {dept.name} ({dept.employeeCount} employees)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <DateRangePicker />
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1" disabled={loading}>
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span>Export</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleExport('csv')}>
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('excel')}>
                Export as Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('pdf')}>
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 md:w-[600px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="utilization">Utilization</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="custom">Custom Reports</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          {loading && !overviewData ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Error loading data: {error}</p>
            </div>
          ) : overviewData ? (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Leave Days</CardTitle>
                    <BarChart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(overviewData.overview.totalLeaveDays)}</div>
                    <p className="text-xs text-muted-foreground">
                      <Badge variant={overviewData.overview.previousPeriodComparison.totalLeaveDaysChange >= 0 ? "default" : "secondary"}>
                        {formatPercentage(overviewData.overview.previousPeriodComparison.totalLeaveDaysChange)}
                      </Badge>
                      {" "}from last {timeRange}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
                    <LineChart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(overviewData.overview.pendingRequests)}</div>
                    <p className="text-xs text-muted-foreground">
                      <Badge variant={overviewData.overview.previousPeriodComparison.pendingRequestsChange >= 0 ? "destructive" : "default"}>
                        {formatPercentage(overviewData.overview.previousPeriodComparison.pendingRequestsChange)}
                      </Badge>
                      {" "}from last {timeRange}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Approval Rate</CardTitle>
                    <PieChart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{overviewData.overview.approvalRate.toFixed(1)}%</div>
                    <p className="text-xs text-muted-foreground">
                      <Badge variant={overviewData.overview.previousPeriodComparison.approvalRateChange >= 0 ? "default" : "destructive"}>
                        {formatPercentage(overviewData.overview.previousPeriodComparison.approvalRateChange)}
                      </Badge>
                      {" "}from last {timeRange}
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg. Response Time</CardTitle>
                    <BarChart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{overviewData.overview.avgResponseTime.toFixed(1)} days</div>
                    <p className="text-xs text-muted-foreground">
                      <Badge variant={overviewData.overview.previousPeriodComparison.avgResponseTimeChange <= 0 ? "default" : "destructive"}>
                        {formatPercentage(overviewData.overview.previousPeriodComparison.avgResponseTimeChange)}
                      </Badge>
                      {" "}from last {timeRange}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">No data available</p>
            </div>
          )}

          {overviewData && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Leave by Type</CardTitle>
                  <CardDescription>
                    Distribution of leave by type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {overviewData.leavesByType && overviewData.leavesByType.length > 0 ? (
                    <div className="space-y-3">
                      {overviewData.leavesByType.map((item: any, index: number) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}
                            />
                            <span className="text-sm font-medium">{item.type}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold">{item.count} requests</div>
                            <div className="text-xs text-muted-foreground">
                              {item.days} days ({item.percentage.toFixed(1)}%)
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center border rounded-md">
                      <div className="text-center">
                        <PieChart className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No leave type data available</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Leave by Department</CardTitle>
                  <CardDescription>
                    Distribution of leave by department
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {overviewData.leavesByDepartment && overviewData.leavesByDepartment.length > 0 ? (
                    <div className="space-y-3">
                      {overviewData.leavesByDepartment.map((item: any, index: number) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: `hsl(${index * 45}, 60%, 55%)` }}
                            />
                            <span className="text-sm font-medium">{item.department}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold">{item.count} requests</div>
                            <div className="text-xs text-muted-foreground">
                              {item.days} days ({item.percentage.toFixed(1)}%)
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center border rounded-md">
                      <div className="text-center">
                        <BarChart className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No department data available</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="utilization" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Leave Utilization</CardTitle>
              <CardDescription>
                Analysis of leave utilization across the organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md">
                <div className="text-center">
                  <BarChart className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">Leave utilization chart will be displayed here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="trends" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Leave Trends</CardTitle>
              <CardDescription>
                Analysis of leave trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md">
                <div className="text-center">
                  <LineChart className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">Leave trends chart will be displayed here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="custom" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom Reports</CardTitle>
              <CardDescription>
                Generate custom leave reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmptyState
                title="Create Custom Report"
                description="Select parameters to generate a custom leave report."
                icon={<FileText className="h-10 w-10 text-muted-foreground" />}
                action={
                  <Button className="mt-4">
                    Generate Report
                  </Button>
                }
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
