import { format } from 'date-fns'

// Define roles that can manage salary structures
const SALARY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.HR_DIRECTOR
]

/**
 * GET handler for generating a salary structure import template
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, SALARY_ADMIN_ROLES)

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Connect to database
    await connectToDatabase()

    // Get all departments
    const departments = await Department.find({ isActive: true }).select('name').lean()

    // Get all roles
    const roles = await Role.find({ isActive: true }).select('name').lean()

    // Create template headers
    const headers = [
      'Name',
      'Description',
      'Effective Date',
      'Expiry Date',
      'Currency',
      'Is Active',
      'Applicable Roles',
      'Applicable Departments',
      'Basic Component Name',
      'Basic Component Amount',
      'Basic Component Is Taxable'
    ]

    // Format current date and future date
    const currentDate = format(new Date(), 'yyyy-MM-dd')
    const futureDate = format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd')

    // Create sample data rows
    const sampleData = [
      [
        'Standard Salary Structure',
        'Standard salary structure for all employees',
        currentDate,
        futureDate,
        'MWK',
        'TRUE',
        'employee,specialist',
        'Human Resources,Finance',
        'Basic Salary',
        '100000',
        'TRUE'
      ],
      [
        'Management Salary Structure',
        'Salary structure for management positions',
        currentDate,
        '',
        'MWK',
        'TRUE',
        'manager,director',
        'Executive Office',
        'Basic Salary',
        '250000',
        'TRUE'
      ],
      [
        'Contract Staff Salary Structure',
        'Salary structure for contract staff',
        currentDate,
        futureDate,
        'MWK',
        'TRUE',
        'contractor',
        '',
        'Contract Fee',
        '150000',
        'FALSE'
      ]
    ]

    // Create a worksheet
    const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData])

    // Create a workbook
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'Salary Structures')

    // Add a roles reference sheet
    const rolesSheet = XLSX.utils.aoa_to_sheet([
      ['Available Roles'],
      ...roles.map(r => [r.name])
    ])
    XLSX.utils.book_append_sheet(wb, rolesSheet, 'Roles Reference')

    // Add a departments reference sheet
    const departmentsSheet = XLSX.utils.aoa_to_sheet([
      ['Available Departments'],
      ...departments.map(d => [d.name])
    ])
    XLSX.utils.book_append_sheet(wb, departmentsSheet, 'Departments Reference')

    // Add an instructions sheet
    const instructionsSheet = XLSX.utils.aoa_to_sheet([
      ['Salary Structure Import Instructions'],
      [''],
      ['Required Fields:'],
      ['Name - The name of the salary structure (required)'],
      ['Effective Date - The date from which the salary structure is effective (required, format: YYYY-MM-DD)'],
      ['Currency - The currency for the salary structure (required, default: MWK)'],
      [''],
      ['Optional Fields:'],
      ['Description - A description of the salary structure (optional)'],
      ['Expiry Date - The date until which the salary structure is effective (optional, format: YYYY-MM-DD)'],
      ['Is Active - Whether the salary structure is active (optional, default: TRUE)'],
      ['Applicable Roles - Comma-separated list of roles this structure applies to (optional)'],
      ['Applicable Departments - Comma-separated list of departments this structure applies to (optional)'],
      ['Basic Component Name - The name of the basic salary component (optional, default: "Basic Salary")'],
      ['Basic Component Amount - The amount for the basic salary component (optional)'],
      ['Basic Component Is Taxable - Whether the basic salary component is taxable (optional, default: TRUE)'],
      [''],
      ['Notes:'],
      ['- For Applicable Roles and Applicable Departments, use the exact names as listed in the reference sheets'],
      ['- Multiple values should be separated by commas (e.g., "employee,specialist")'],
      ['- Dates should be in YYYY-MM-DD format'],
      ['- Boolean values should be TRUE or FALSE'],
      ['- The Basic Component will be created automatically for each salary structure'],
      ['- Additional components can be added after importing the salary structure']
    ])
    XLSX.utils.book_append_sheet(wb, instructionsSheet, 'Instructions')

    // Generate Excel file
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })

    // Log template generation
    logger.info('Salary structure import template generated', LogCategory.EXPORT, {
      userId: user.id,
      rolesCount: roles.length,
      departmentsCount: departments.length
    })

    // Convert Buffer to Uint8Array for Response
    const uint8Array = new Uint8Array(buffer);

export const runtime = 'nodejs';

// app/api/payroll/salary-structures/template/route.ts
    // Return the template file
    return new Response(uint8Array, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="salary-structure-import-template.xlsx"',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error: unknown) {
    console.error('Error generating salary structure import template:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    )
  }
}