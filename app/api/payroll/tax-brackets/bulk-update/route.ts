import logger, { LogCategory } from '@/lib/backend/utils/logger'

// Define roles that can bulk update tax brackets
const TAX_BRACKET_UPDATE_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER
]

interface BulkUpdateRequest {
  action: 'activate' | 'deactivate' | 'update_rates' | 'set_expiry' | 'delete';

export const runtime = 'nodejs';

// app/api/payroll/tax-brackets/bulk-update/route.ts
  filters: {
    country?: string;
    currency?: string;
    effectiveDate?: string;
    isActive?: boolean;
    ids?: string[];
  };
  updates?: {
    isActive?: boolean;
    expiryDate?: string;
    rateMultiplier?: number; // For percentage-based rate updates
    rateAdjustment?: number; // For fixed amount rate adjustments
  };
}
/**
 * POST handler for bulk updating tax brackets
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, TAX_BRACKET_UPDATE_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }
    // Connect to database
    await connectToDatabase()
    // Get request body
    const body: BulkUpdateRequest = await request.json()
    // Validate request
    if (!body.action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 })
    }
    if (!body.filters) {
      return NextResponse.json({ error: 'Filters are required' }, { status: 400 })
    }
    // Build query from filters
    const query: any = {}
    if (body.filters.country) {
      query.country = body.filters.country
    }
    if (body.filters.currency) {
      query.currency = body.filters.currency
    }
    if (body.filters.effectiveDate) {
      query.effectiveDate = new Date(body.filters.effectiveDate)
    }
    if (body.filters.isActive !== undefined) {
      query.isActive = body.filters.isActive
    }
    if (body.filters.ids && body.filters.ids.length > 0) {
      query._id = { $in: body.filters.ids }
    }
    logger.info('Starting bulk tax bracket update', LogCategory.API, {
      userId: user.id,
      action: body.action,
      query,
      updates: body.updates
    })
    let result: any = {}
    let affectedCount = 0
    switch (body.action) {
      case 'activate':
        // Activate selected tax brackets
        result = await TaxBracket.updateMany(
          query,
          {
            $set: {
              isActive: true,
              updatedBy: user.id
            }
          }
        )
        affectedCount = result.modifiedCount
        break
      case 'deactivate':
        // Deactivate selected tax brackets
        result = await TaxBracket.updateMany(
          query,
          {
            $set: {
              isActive: false,
              updatedBy: user.id
            }
          }
        )
        affectedCount = result.modifiedCount
        break
      case 'update_rates':
        // Update tax rates for selected brackets
        if (!body.updates || (body.updates.rateMultiplier === undefined && body.updates.rateAdjustment === undefined)) {
          return NextResponse.json({ 
            error: 'Rate multiplier or rate adjustment is required for rate updates' 
          }, { status: 400 })
        }
        // Get tax brackets to update
        const bracketsToUpdate = await TaxBracket.find(query)
        for (const bracket of bracketsToUpdate) {
          // Update each bracket's rates
          const updatedBrackets = bracket.brackets.map((b: any) => {
            let newRate = b.rate
            if (body.updates!.rateMultiplier !== undefined) {
              newRate = b.rate * body.updates!.rateMultiplier
            }
            if (body.updates!.rateAdjustment !== undefined) {
              newRate = b.rate + body.updates!.rateAdjustment
            }
            // Ensure rate doesn't go below 0 or above 100
            newRate = Math.max(0, Math.min(100, newRate))
            return {
              ...b,
              rate: newRate
            }
          })
          bracket.brackets = updatedBrackets
          bracket.updatedBy = user.id
          await bracket.save()
          affectedCount++
        }
        break
      case 'set_expiry':
        // Set expiry date for selected tax brackets
        if (!body.updates || !body.updates.expiryDate) {
          return NextResponse.json({ 
            error: 'Expiry date is required for setting expiry' 
          }, { status: 400 })
        }
        result = await TaxBracket.updateMany(
          query,
          {
            $set: {
              expiryDate: new Date(body.updates.expiryDate),
              updatedBy: user.id
            }
          }
        )
        affectedCount = result.modifiedCount
        break
      case 'delete':
        // Delete selected tax brackets
        // First check if any are currently active and warn
        const activeBrackets = await TaxBracket.countDocuments({
          ...query,
          isActive: true
        })
        if (activeBrackets > 0) {
          return NextResponse.json({
            error: `Cannot delete ${activeBrackets} active tax brackets. Please deactivate them first.`
          }, { status: 400 })
        }
        result = await TaxBracket.deleteMany(query)
        affectedCount = result.deletedCount
        break
      default:
        return NextResponse.json({ 
          error: 'Invalid action. Supported actions: activate, deactivate, update_rates, set_expiry, delete' 
        }, { status: 400 })
    }
    logger.info('Bulk tax bracket update completed', LogCategory.API, {
      userId: user.id,
      action: body.action,
      affectedCount
    })
    return NextResponse.json({
      success: true,
      message: `Successfully ${body.action.replace('_', ' ')}d ${affectedCount} tax bracket(s)`,
      data: {
        action: body.action,
        affectedCount,
        query: body.filters
      }
    })
  } catch (error) {
    logger.error('Error in bulk tax bracket update', LogCategory.API, error)
    return NextResponse.json(
      { 
        error: 'Failed to perform bulk update', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}