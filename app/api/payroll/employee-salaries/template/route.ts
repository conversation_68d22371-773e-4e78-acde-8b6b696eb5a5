import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/services/auth/AuthService'
import { hasRequiredPermissions, UserRole } from '@/lib/backend/utils/permissions'
import { connectToDatabase } from '@/lib/backend/database/connection'
import { Employee } from '@/lib/backend/models/hr/Employee'
import { EmployeeSalary } from '@/lib/backend/models/payroll/EmployeeSalary'
import { SalaryStructure } from '@/lib/backend/models/payroll/SalaryStructure'
import { Allowance } from '@/lib/backend/models/payroll/Allowance'
import { Deduction } from '@/lib/backend/models/payroll/Deduction'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'
import mongoose from 'mongoose'

export const runtime = 'nodejs'

// Define interfaces for type safety
interface PopulatedDepartment {
  _id: mongoose.Types.ObjectId;
  name: string;
}
interface EmployeeWithDepartment {
  _id: mongoose.Types.ObjectId;
  firstName: string;
  lastName: string;
  email: string;
  employeeId?: string;
  employeeNumber?: string;
  position?: string;
  salary?: number;
  departmentId?: PopulatedDepartment;
}
interface SalaryStructureData {
  _id: mongoose.Types.ObjectId;
  name: string;
}
interface AllowanceData {
  _id: mongoose.Types.ObjectId;
  name: string;
}
interface DeductionData {
  _id: mongoose.Types.ObjectId;
  name: string;
}
// Define roles that can download employee salary templates
const EMPLOYEE_SALARY_ADMIN_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.HR_DIRECTOR,
  UserRole.HR_MANAGER,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER
]
/**
 * GET handler for generating an employee salary import template
 * @param request - Next.js request
 * @returns Next.js response
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, EMPLOYEE_SALARY_ADMIN_ROLES)
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }
    // Connect to database
    await connectToDatabase()
    // Get ALL active employees for the template (not just sample)
    const allEmployees = await Employee.find({
      employmentStatus: 'active',
      isBlocked: { $ne: true } // Exclude blocked employees
    })
      .select('firstName lastName email employeeId employeeNumber position departmentId salary')
      .populate('departmentId', 'name')
      .sort({ lastName: 1, firstName: 1 })
      .lean<EmployeeWithDepartment[]>()
    // Get employees who already have salary records to mark them
    const existingSalaryEmployeeIds = await EmployeeSalary.find({ isActive: true })
      .distinct('employeeId')
    const existingSalaryEmployeeIdsSet = new Set(
      existingSalaryEmployeeIds.map((id: mongoose.Types.ObjectId) => id.toString())
    )
    const salaryStructures = await SalaryStructure.find({ isActive: true })
      .select('name')
      .lean<SalaryStructureData[]>()
    const allowances = await Allowance.find({ isActive: true })
      .limit(5)
      .select('name')
      .lean<AllowanceData[]>()
    const deductions = await Deduction.find({ isActive: true })
      .limit(5)
      .select('name')
      .lean<DeductionData[]>()
    // Create template headers with additional employee info for validation
    const headers = [
      'Employee Email',
      'Employee ID',
      'Employee Number',
      'Employee Name',
      'Position',
      'Department',
      'Current Salary',
      'Has Existing Salary Record',
      'Basic Salary',
      'Currency',
      'Salary Structure',
      'Effective Date',
      'End Date',
      'Bank Name',
      'Bank Account Number',
      'Bank Branch Code',
      'Payment Method',
      'Tax ID',
      'Pension Scheme',
      'Pension Number',
      'Notes',
      'Is Active',
      // Allowances
      'Allowance 1 Name',
      'Allowance 1 Amount',
      'Allowance 1 Percentage',
      'Allowance 1 Is Taxable',
      'Allowance 2 Name',
      'Allowance 2 Amount',
      'Allowance 2 Percentage',
      'Allowance 2 Is Taxable',
      'Allowance 3 Name',
      'Allowance 3 Amount',
      'Allowance 3 Percentage',
      'Allowance 3 Is Taxable',
      // Deductions
      'Deduction 1 Name',
      'Deduction 1 Amount',
      'Deduction 1 Percentage',
      'Deduction 2 Name',
      'Deduction 2 Amount',
      'Deduction 2 Percentage',
      'Deduction 3 Name',
      'Deduction 3 Amount',
      'Deduction 3 Percentage'
    ]
    // Format current date
    const currentDate = format(new Date(), 'yyyy-MM-dd')
    // Create employee data rows from real employee records
    const employeeData: (string | number)[][] = []
    // Add all active employees to the template
    if (allEmployees.length > 0) {
      allEmployees.forEach((employee: EmployeeWithDepartment, index: number) => {
        const hasExistingSalary = existingSalaryEmployeeIdsSet.has(employee._id.toString())
        const salaryStructureName = salaryStructures[index % salaryStructures.length]?.name || ''
        // Get department name with proper type checking
        const departmentName = employee.departmentId?.name || 'No Department'
        employeeData.push([
          employee.email || '', // Employee Email
          employee.employeeId || '', // Employee ID
          employee.employeeNumber || '', // Employee Number
          `${employee.firstName || ''} ${employee.lastName || ''}`.trim(), // Employee Name
          employee.position || '', // Position
          departmentName, // Department
          employee.salary || '', // Current Salary
          hasExistingSalary ? 'YES' : 'NO', // Has Existing Salary Record
          employee.salary || '', // Basic Salary (pre-filled with current salary)
          'MWK', // Currency
          salaryStructureName, // Salary Structure
          currentDate, // Effective Date
          '', // End Date (optional)
          '', // Bank Name
          '', // Bank Account Number
          '', // Bank Branch Code
          'bank_transfer', // Payment Method
          '', // Tax ID
          '', // Pension Scheme
          '', // Pension Number
          '', // Notes
          hasExistingSalary ? 'false' : 'true', // Is Active (false if already has salary)
          // Allowances - Leave empty to avoid validation errors
          '', // Allowance 1 Name
          '', // Allowance 1 Amount
          '', // Allowance 1 Percentage
          '', // Allowance 1 Is Taxable
          '', // Allowance 2 Name
          '', // Allowance 2 Amount
          '', // Allowance 2 Percentage
          '', // Allowance 2 Is Taxable
          '', // Allowance 3 Name
          '', // Allowance 3 Amount
          '', // Allowance 3 Percentage
          '', // Allowance 3 Is Taxable
          // Deductions - Leave empty to avoid validation errors
          '', // Deduction 1 Name
          '', // Deduction 1 Amount
          '', // Deduction 1 Percentage
          '', // Deduction 2 Name
          '', // Deduction 2 Amount
          '', // Deduction 2 Percentage
          '', // Deduction 3 Name
          '', // Deduction 3 Amount
          '' // Deduction 3 Percentage
        ])
      })
    } else {
      // Add default message if no employees found
      employeeData.push([
        'No active employees found in the system',
        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
      ])
    }
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new()
    // Create main data sheet
    const worksheetData = [headers, ...employeeData]
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
    // Set column widths for better readability
    const columnWidths = headers.map((header) => {
      if (header.includes('Name') || header.includes('Email') || header.includes('Department')) return { wch: 25 }
      if (header.includes('Date') || header.includes('Amount') || header.includes('Percentage')) return { wch: 15 }
      if (header.includes('Notes')) return { wch: 30 }
      if (header.includes('Position')) return { wch: 20 }
      return { wch: 15 }
    })
    worksheet['!cols'] = columnWidths
    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Employee Salaries')
    // Create instructions sheet
    const instructions: string[][] = [
      ['Employee Salary Bulk Import Instructions - Enhanced Version'],
      [''],
      ['OVERVIEW:'],
      ['This template contains ALL active employees from your system.'],
      ['Employee data is pre-populated for validation and convenience.'],
      [''],
      ['IMPORTANT VALIDATION FEATURES:'],
      ['✓ Employee Email: Primary identifier for validation'],
      ['✓ Employee ID/Number: Alternative identifiers'],
      ['✓ Current Salary: Shows employee\'s existing salary from their record'],
      ['✓ Has Existing Salary Record: Shows YES if employee already has a salary record'],
      [''],
      ['PROCESSING LOGIC:'],
      ['• Employees are validated by email against the database'],
      ['• Non-existent employees are filtered out (not imported)'],
      ['• Employees with existing salary records are skipped'],
      ['• Import continues even if some employees fail validation'],
      ['• Comprehensive results show: imported, failed, and skipped employees'],
      [''],
      ['REQUIRED FIELDS:'],
      ['- Employee Email: Must match an existing employee in the system'],
      ['- Basic Salary: Must be a positive number'],
      ['- Effective Date: Format as YYYY-MM-DD'],
      [''],
      ['OPTIONAL FIELDS:'],
      ['- Currency: Defaults to MWK if not specified'],
      ['- Salary Structure: Must match existing salary structure name'],
      ['- End Date: Format as YYYY-MM-DD (leave blank for ongoing)'],
      ['- Bank Details: For payment processing'],
      ['- Payment Method: bank_transfer, cash, check, or mobile_money'],
      ['- Is Active: true/false (defaults to true, set to false for existing records)'],
      [''],
      ['ALLOWANCES AND DEDUCTIONS:'],
      ['- You can specify up to 3 allowances and 3 deductions per employee'],
      ['- IMPORTANT: If you provide an allowance/deduction name, you MUST also provide either Amount OR Percentage'],
      ['- Leave allowance/deduction name blank if you don\'t want to add any'],
      ['- Amount: Fixed amount in the specified currency (e.g., 25000)'],
      ['- Percentage: Percentage of basic salary (e.g., 10 for 10%)'],
      ['- Is Taxable: true/false for allowances (required if allowance name is provided)'],
      [''],
      ['BEST PRACTICES:'],
      ['- Review "Has Existing Salary Record" column before importing'],
      ['- Set "Is Active" to false for employees who already have salary records'],
      ['- Use the pre-filled "Current Salary" as a reference'],
      ['- Verify employee emails are correct before importing'],
      ['- Check the results modal after import for detailed feedback'],
      [''],
      ['VALIDATION RESULTS:'],
      ['After import, you will see:'],
      ['• Successfully imported employees'],
      ['• Failed employees (with reasons)'],
      ['• Skipped employees (duplicates/existing records)'],
      ['• Non-existent employees (filtered out)']
    ]
    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions)
    instructionsSheet['!cols'] = [{ wch: 50 }]
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions')
    // Create reference data sheet
    const referenceData: (string | number)[][] = [
      ['Available Salary Structures'],
      ...salaryStructures.map((structure: SalaryStructureData) => [structure.name]),
      [''],
      ['Available Allowances'],
      ...allowances.map((allowance: AllowanceData) => [allowance.name]),
      [''],
      ['Available Deductions'],
      ...deductions.map((deduction: DeductionData) => [deduction.name]),
      [''],
      ['ALLOWANCE/DEDUCTION EXAMPLES'],
      ['Name', 'Amount', 'Percentage', 'Notes'],
      ['Housing Allowance', '25000', '', 'Fixed amount example'],
      ['Transport Allowance', '', '10', 'Percentage example (10% of basic salary)'],
      ['PAYE Tax', '', '30', 'Percentage deduction example'],
      ['Pension Contribution', '', '5', 'Percentage deduction example'],
      [''],
      ['IMPORTANT RULES:'],
      ['- If you provide a name, you MUST provide either Amount OR Percentage'],
      ['- Leave name blank if you don\'t want to add allowances/deductions'],
      ['- Amount: Fixed value in MWK'],
      ['- Percentage: Percentage of basic salary (without % symbol)']
    ]
    const referenceSheet = XLSX.utils.aoa_to_sheet(referenceData)
    referenceSheet['!cols'] = [{ wch: 30 }]
    XLSX.utils.book_append_sheet(workbook, referenceSheet, 'Reference Data')
    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })
    // Convert Buffer to Uint8Array for Response
    const uint8Array = new Uint8Array(buffer);
    // Return the template file
    return new Response(uint8Array, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="employee-salary-import-template.xlsx"',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error: unknown) {
    console.error('Error generating employee salary import template:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    )
  }
}