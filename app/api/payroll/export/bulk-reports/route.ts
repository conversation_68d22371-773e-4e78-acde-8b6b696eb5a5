import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/backend/services/auth/AuthService'
import { hasRequiredPermissions, UserRole } from '@/lib/backend/utils/permissions'
import { connectToDatabase } from '@/lib/backend/database/connection'
import { PaySlip } from '@/lib/backend/models/payroll/PaySlip'
import * as XLSX from 'xlsx'
import logger, { LogCategory } from "@/lib/backend/utils/logger"

export const runtime = 'nodejs'

// Required permissions for bulk reports export
const REQUIRED_ROLES = [
  UserRole.SUPER_ADMIN,
  UserRole.SYSTEM_ADMIN,
  UserRole.FINANCE_DIRECTOR,
  UserRole.FINANCE_MANAGER,
  UserRole.PAYROLL_SPECIALIST,
  UserRole.HR_DIRECTOR
]

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, REQUIRED_ROLES)
    if (!hasPermission) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 })
    }

    await connectToDatabase()

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const reportType = searchParams.get('reportType') || 'payroll_summary' // payroll_summary, tax_summary, department_summary, employee_summary
    const format = searchParams.get('format') || 'excel' // excel, pdf, csv
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const department = searchParams.get('department') || undefined
    const payrollRunId = searchParams.get('payrollRunId') || undefined

    // Validate date range
    if (!startDate || !endDate) {
      return NextResponse.json({ error: "Start date and end date are required" }, { status: 400 })
    }

    // Generate report based on type
    switch (reportType.toLowerCase()) {
      case 'payroll_summary':
        return await generatePayrollSummaryReport(format, startDate, endDate, department, payrollRunId)
      case 'tax_summary':
        return await generateTaxSummaryReport(format, startDate, endDate, department)
      case 'department_summary':
        return await generateDepartmentSummaryReport(format, startDate, endDate)
      case 'employee_summary':
        return await generateEmployeeSummaryReport(format, startDate, endDate, department)
      default:
        return NextResponse.json({ error: "Invalid report type. Supported types: payroll_summary, tax_summary, department_summary, employee_summary" }, { status: 400 })
    }

  } catch (error) {
    logger.error("Bulk reports export error", LogCategory.PAYROLL, error)
    return NextResponse.json(
      { error: "Failed to export reports" },
      { status: 500 }
    )
  }
}

interface PaySlipData {
  _id: string;
  employeeId: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    department: string;
    position: string;
    employeeNumber: string;
  };
  payrollRunId: {
    _id: string;
    name: string;
    payPeriod: {
      startDate: Date;
      endDate: Date;
    };
    status: string;
  };
  employeeDetails: {
    name: string;
    employeeNumber: string;
    department?: string;
    position?: string;
  };
  paymentDetails: {
    grossSalary: number;
    netSalary: number;
    totalDeductions: number;
    totalTax: number;
    currency: string;
  };
  earnings: Array<{
    name: string;
    amount: number;
    isTaxable: boolean;
  }>;
  deductions: Array<{
    name: string;
    amount: number;
    isStatutory: boolean;
  }>;
  payPeriod: {
    startDate: Date;
    endDate: Date;
    month: number;
    year: number;
  };
  status: string;
}
async function generatePayrollSummaryReport(format: string, startDate: string, endDate: string, department?: string, payrollRunId?: string) {
  // Build query for PaySlip
  let query: any = {
    'payPeriod.startDate': { $gte: new Date(startDate) },
    'payPeriod.endDate': { $lte: new Date(endDate) },
    status: { $ne: 'cancelled' } // Exclude cancelled payslips
  }
  if (payrollRunId) {
    query.payrollRunId = payrollRunId
  }
  // Get payslips with employee data
  const payslips = await PaySlip.find(query)
    .populate('employeeId', 'firstName lastName email department position employeeNumber')
    .populate('payrollRunId', 'name payPeriod status')
    .sort({ 'payPeriod.startDate': -1 })
    .lean() as unknown as PaySlipData[]
  // Filter by department if specified
  const filteredPayslips = department
    ? payslips.filter((payslip: PaySlipData) =>
        payslip.employeeDetails?.department === department ||
        payslip.employeeId?.department === department
      )
    : payslips
  // Calculate summary statistics
  const summary = {
    totalEmployees: new Set(filteredPayslips.map((p: PaySlipData) => p.employeeId?._id?.toString())).size,
    totalPayslips: filteredPayslips.length,
    totalGrossSalary: filteredPayslips.reduce((sum: number, p: PaySlipData) => sum + (p.paymentDetails?.grossSalary || 0), 0),
    totalNetSalary: filteredPayslips.reduce((sum: number, p: PaySlipData) => sum + (p.paymentDetails?.netSalary || 0), 0),
    totalAllowances: filteredPayslips.reduce((sum: number, p: PaySlipData) => {
      const allowances = p.earnings?.filter(e => !e.isStatutory)?.reduce((eSum, e) => eSum + e.amount, 0) || 0
      return sum + allowances
    }, 0),
    totalDeductions: filteredPayslips.reduce((sum: number, p: PaySlipData) => sum + (p.paymentDetails?.totalDeductions || 0), 0),
    totalTax: filteredPayslips.reduce((sum: number, p: PaySlipData) => sum + (p.paymentDetails?.totalTax || 0), 0)
  }
  switch (format.toLowerCase()) {
    case 'excel':
      return await generatePayrollSummaryExcel(filteredPayslips, summary, startDate, endDate)
    case 'pdf':
      return await generatePayrollSummaryPDF(filteredPayslips, summary, startDate, endDate)
    case 'csv':
      return await generatePayrollSummaryCSV(filteredPayslips, summary)
    default:
      return NextResponse.json({ error: "Invalid format. Supported formats: excel, pdf, csv" }, { status: 400 })
  }
}
async function generateTaxSummaryReport(format: string, startDate: string, endDate: string, department?: string) {
  // Get payslips for tax analysis
  let query: any = {
    'payPeriod.startDate': { $gte: new Date(startDate) },
    'payPeriod.endDate': { $lte: new Date(endDate) },
    status: { $ne: 'cancelled' }
  }
  const payslips = await PaySlip.find(query)
    .populate('employeeId', 'firstName lastName email department position employeeNumber')
    .sort({ 'payPeriod.startDate': -1 })
    .lean() as unknown as PaySlipData[]
  // Filter by department if specified
  const filteredPayslips = department
    ? payslips.filter((payslip: PaySlipData) =>
        payslip.employeeDetails?.department === department ||
        payslip.employeeId?.department === department
      )
    : payslips
  // Group by tax brackets and calculate totals
  const taxSummary = filteredPayslips.reduce((acc: any, payslip: PaySlipData) => {
    const taxAmount = payslip.paymentDetails?.totalTax || 0
    const grossSalary = payslip.paymentDetails?.grossSalary || 0
    const department = payslip.employeeDetails?.department || payslip.employeeId?.department || 'Unknown'
    if (!acc[department]) {
      acc[department] = {
        employeeCount: new Set(),
        totalGross: 0,
        totalTax: 0,
        averageTaxRate: 0
      }
    }
    acc[department].employeeCount.add(payslip.employeeId?._id?.toString())
    acc[department].totalGross += grossSalary
    acc[department].totalTax += taxAmount
    return acc
  }, {})
  // Calculate average tax rates
  Object.keys(taxSummary).forEach(dept => {
    const data = taxSummary[dept]
    data.employeeCount = data.employeeCount.size
    data.averageTaxRate = data.totalGross > 0 ? (data.totalTax / data.totalGross) * 100 : 0
  })
  switch (format.toLowerCase()) {
    case 'excel':
      return await generateTaxSummaryExcel(taxSummary, startDate, endDate)
    case 'pdf':
      return await generateTaxSummaryPDF(taxSummary, startDate, endDate)
    case 'csv':
      return await generateTaxSummaryCSV(taxSummary)
    default:
      return NextResponse.json({ error: "Invalid format" }, { status: 400 })
  }
}
async function generateDepartmentSummaryReport(format: string, startDate: string, endDate: string) {
  // Get payslips grouped by department
  const payslips = await PaySlip.find({
    'payPeriod.startDate': { $gte: new Date(startDate) },
    'payPeriod.endDate': { $lte: new Date(endDate) },
    status: { $ne: 'cancelled' }
  })
    .populate('employeeId', 'firstName lastName department position')
    .lean() as unknown as PaySlipData[]
  // Group by department
  const departmentSummary = payslips.reduce((acc: any, payslip: PaySlipData) => {
    const department = payslip.employeeDetails?.department || payslip.employeeId?.department || 'Unknown'
    if (!acc[department]) {
      acc[department] = {
        employeeCount: new Set(),
        totalPayslips: 0,
        totalGross: 0,
        totalNet: 0,
        totalAllowances: 0,
        totalDeductions: 0,
        averageGross: 0,
        averageNet: 0
      }
    }
    acc[department].employeeCount.add(payslip.employeeId?._id?.toString())
    acc[department].totalPayslips += 1
    acc[department].totalGross += payslip.paymentDetails?.grossSalary || 0
    acc[department].totalNet += payslip.paymentDetails?.netSalary || 0
    acc[department].totalAllowances += payslip.earnings?.filter(e => !e.isStatutory)?.reduce((sum, e) => sum + e.amount, 0) || 0
    acc[department].totalDeductions += payslip.paymentDetails?.totalDeductions || 0
    return acc
  }, {})
  // Calculate averages
  Object.keys(departmentSummary).forEach(dept => {
    const data = departmentSummary[dept]
    const employeeCount = data.employeeCount.size
    data.employeeCount = employeeCount
    data.averageGross = employeeCount > 0 ? data.totalGross / employeeCount : 0
    data.averageNet = employeeCount > 0 ? data.totalNet / employeeCount : 0
  })
  switch (format.toLowerCase()) {
    case 'excel':
      return await generateDepartmentSummaryExcel(departmentSummary, startDate, endDate)
    case 'pdf':
      return await generateDepartmentSummaryPDF(departmentSummary, startDate, endDate)
    case 'csv':
      return await generateDepartmentSummaryCSV(departmentSummary)
    default:
      return NextResponse.json({ error: "Invalid format" }, { status: 400 })
  }
}
async function generateEmployeeSummaryReport(format: string, startDate: string, endDate: string, department?: string) {
  // Get employee payroll summary
  let query: any = {
    'payPeriod.startDate': { $gte: new Date(startDate) },
    'payPeriod.endDate': { $lte: new Date(endDate) },
    status: { $ne: 'cancelled' }
  }
  const payslips = await PaySlip.find(query)
    .populate('employeeId', 'firstName lastName email department position employeeNumber')
    .sort({ 'employeeId.lastName': 1 })
    .lean() as unknown as PaySlipData[]
  // Filter by department if specified
  const filteredPayslips = department
    ? payslips.filter((payslip: PaySlipData) =>
        payslip.employeeDetails?.department === department ||
        payslip.employeeId?.department === department
      )
    : payslips
  // Group by employee
  const employeeSummary = filteredPayslips.reduce((acc: any, payslip: PaySlipData) => {
    const employeeId = payslip.employeeId?._id?.toString()
    if (!employeeId) return acc
    if (!acc[employeeId]) {
      acc[employeeId] = {
        employee: payslip.employeeId,
        payslipCount: 0,
        totalGross: 0,
        totalNet: 0,
        totalAllowances: 0,
        totalDeductions: 0,
        totalTax: 0,
        averageGross: 0,
        averageNet: 0
      }
    }
    acc[employeeId].payslipCount += 1
    acc[employeeId].totalGross += payslip.paymentDetails?.grossSalary || 0
    acc[employeeId].totalNet += payslip.paymentDetails?.netSalary || 0
    acc[employeeId].totalAllowances += payslip.earnings?.filter(e => !e.isStatutory)?.reduce((sum, e) => sum + e.amount, 0) || 0
    acc[employeeId].totalDeductions += payslip.paymentDetails?.totalDeductions || 0
    acc[employeeId].totalTax += payslip.paymentDetails?.totalTax || 0
    return acc
  }, {})
  // Calculate averages
  Object.keys(employeeSummary).forEach(empId => {
    const data = employeeSummary[empId]
    data.averageGross = data.payslipCount > 0 ? data.totalGross / data.payslipCount : 0
    data.averageNet = data.payslipCount > 0 ? data.totalNet / data.payslipCount : 0
  })
  const employeeData = Object.values(employeeSummary)
  switch (format.toLowerCase()) {
    case 'excel':
      return await generateEmployeeSummaryExcel(employeeData, startDate, endDate)
    case 'pdf':
      return await generateEmployeeSummaryPDF(employeeData, startDate, endDate)
    case 'csv':
      return await generateEmployeeSummaryCSV(employeeData)
    default:
      return NextResponse.json({ error: "Invalid format" }, { status: 400 })
  }
}
// Excel generation functions
async function generatePayrollSummaryExcel(payslips: PaySlipData[], summary: any, startDate: string, endDate: string) {
  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new()
  // Summary data
  const summaryData = [
    ['Payroll Summary Report'],
    ['Period:', `${startDate} to ${endDate}`],
    ['Generated:', new Date().toLocaleDateString()],
    [],
    ['Metric', 'Value'],
    ['Total Employees', summary.totalEmployees],
    ['Total Payslips', summary.totalPayslips],
    ['Total Gross Salary', `MWK ${summary.totalGrossSalary.toLocaleString()}`],
    ['Total Net Salary', `MWK ${summary.totalNetSalary.toLocaleString()}`],
    ['Total Allowances', `MWK ${summary.totalAllowances.toLocaleString()}`],
    ['Total Deductions', `MWK ${summary.totalDeductions.toLocaleString()}`],
    ['Total Tax', `MWK ${summary.totalTax.toLocaleString()}`]
  ]
  // Create summary worksheet
  const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary')
  // Create detailed payslips data
  const payslipHeaders = [
    'Employee Name',
    'Employee Number',
    'Department',
    'Position',
    'Pay Period',
    'Gross Salary',
    'Total Allowances',
    'Total Deductions',
    'Total Tax',
    'Net Salary',
    'Status'
  ]
  const payslipData = payslips.map(payslip => [
    payslip.employeeDetails?.name || `${payslip.employeeId?.firstName} ${payslip.employeeId?.lastName}`,
    payslip.employeeDetails?.employeeNumber || payslip.employeeId?.employeeNumber,
    payslip.employeeDetails?.department || payslip.employeeId?.department,
    payslip.employeeDetails?.position || payslip.employeeId?.position,
    `${payslip.payPeriod?.startDate ? new Date(payslip.payPeriod.startDate).toLocaleDateString() : ''} - ${payslip.payPeriod?.endDate ? new Date(payslip.payPeriod.endDate).toLocaleDateString() : ''}`,
    payslip.paymentDetails?.grossSalary || 0,
    payslip.earnings?.filter(e => !e.isStatutory)?.reduce((sum, e) => sum + e.amount, 0) || 0,
    payslip.paymentDetails?.totalDeductions || 0,
    payslip.paymentDetails?.totalTax || 0,
    payslip.paymentDetails?.netSalary || 0,
    payslip.status || 'Unknown'
  ])
  // Create detailed worksheet
  const detailedData = [payslipHeaders, ...payslipData]
  const detailedWorksheet = XLSX.utils.aoa_to_sheet(detailedData)
  XLSX.utils.book_append_sheet(workbook, detailedWorksheet, 'Detailed Payslips')
  // Generate buffer
  const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })
  const filename = `payroll_summary_${startDate}_to_${endDate}.xlsx`
  return new NextResponse(buffer, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Content-Length': buffer.byteLength.toString()
    }
  })
}
async function generatePayrollSummaryPDF(_payslips: PaySlipData[], _summary: any, _startDate: string, _endDate: string) {
  // PDF generation not implemented - would require additional PDF library
  return new NextResponse("PDF generation not yet implemented", { status: 501 })
}
async function generatePayrollSummaryCSV(_payslips: PaySlipData[], summary: any) {
  const csvData = [
    ['Metric', 'Value'],
    ['Total Employees', summary.totalEmployees],
    ['Total Payslips', summary.totalPayslips],
    ['Total Gross Salary', summary.totalGrossSalary],
    ['Total Net Salary', summary.totalNetSalary],
    ['Total Allowances', summary.totalAllowances],
    ['Total Deductions', summary.totalDeductions],
    ['Total Tax', summary.totalTax]
  ]
  const csvContent = csvData.map((row: any[]) => row.join(',')).join('\n')
  const filename = `payroll_summary_${new Date().toISOString().split('T')[0]}.csv`
  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`
    }
  })
}
// Placeholder functions for other report types (would be implemented similarly)
async function generateTaxSummaryExcel(_taxSummary: any, _startDate: string, _endDate: string) {
  // Implementation similar to generatePayrollSummaryExcel
  return new NextResponse("Tax summary Excel generation not yet implemented", { status: 501 })
}
async function generateTaxSummaryPDF(_taxSummary: any, _startDate: string, _endDate: string) {
  // Implementation similar to generatePayrollSummaryPDF
  return new NextResponse("Tax summary PDF generation not yet implemented", { status: 501 })
}
async function generateTaxSummaryCSV(_taxSummary: any) {
  // Implementation similar to generatePayrollSummaryCSV
  return new NextResponse("Tax summary CSV generation not yet implemented", { status: 501 })
}
async function generateDepartmentSummaryExcel(_departmentSummary: any, _startDate: string, _endDate: string) {
  return new NextResponse("Department summary Excel generation not yet implemented", { status: 501 })
}
async function generateDepartmentSummaryPDF(_departmentSummary: any, _startDate: string, _endDate: string) {
  return new NextResponse("Department summary PDF generation not yet implemented", { status: 501 })
}
async function generateDepartmentSummaryCSV(_departmentSummary: any) {
  return new NextResponse("Department summary CSV generation not yet implemented", { status: 501 })
}
async function generateEmployeeSummaryExcel(_employeeData: any[], _startDate: string, _endDate: string) {
  return new NextResponse("Employee summary Excel generation not yet implemented", { status: 501 })
}
async function generateEmployeeSummaryPDF(_employeeData: any[], _startDate: string, _endDate: string) {
  return new NextResponse("Employee summary PDF generation not yet implemented", { status: 501 })
}
async function generateEmployeeSummaryCSV(_employeeData: any[]) {
  return new NextResponse("Employee summary CSV generation not yet implemented", { status: 501 })
}