import logger, { LogCategory } from '@/lib/backend/utils/logger'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.HR_STAFF
    ])

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Connect to database
    await connectToDatabase()

    // Generate template
    const importExportService = new EmployeeImportExportService()
    const buffer = await importExportService.generateImportTemplate()

    // Return template file
    logger.info('Employee import template downloaded', LogCategory.EXPORT, {
      userId: user.id,
      userEmail: user.email
    })

    // Convert Buffer to Uint8Array which is acceptable for NextResponse
    const uint8Array = new Uint8Array(buffer);

export const runtime = 'nodejs';

// app/api/employees/template/route.ts
    return new NextResponse(uint8Array, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="employee-import-template.xlsx"',
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error: unknown) {
    logger.error('Error generating employee import template', LogCategory.EXPORT, error)
    return NextResponse.json({ error: error instanceof Error ? error.message : 'An error occurred while generating employee import template' }, { status: 500 })
  }
}