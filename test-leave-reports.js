// Simple test script to verify leave reports functionality
const mongoose = require('mongoose');

// Connect to MongoDB
async function testLeaveReports() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kawandama-hr');
    console.log('✅ Connected to MongoDB');

    // Check if Leave model exists
    const Leave = mongoose.models.Leave || mongoose.model('Leave', new mongoose.Schema({
      leaveId: String,
      employeeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Employee' },
      leaveTypeId: { type: mongoose.Schema.Types.ObjectId, ref: 'LeaveType' },
      startDate: Date,
      endDate: Date,
      duration: Number,
      reason: String,
      status: { type: String, enum: ['pending', 'approved', 'rejected', 'cancelled'] },
      approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      approvalDate: Date,
      rejectionReason: String,
      attachments: [String],
      notes: String,
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check if LeaveType model exists
    const LeaveType = mongoose.models.LeaveType || mongoose.model('LeaveType', new mongoose.Schema({
      name: String,
      code: String,
      description: String,
      defaultDays: Number,
      isActive: Boolean,
      isPaid: Boolean,
      requiresApproval: Boolean,
      maxConsecutiveDays: Number,
      minNoticeInDays: Number,
      allowCarryOver: Boolean,
      maxCarryOverDays: Number,
      color: String,
      applicableRoles: [String],
      applicableDepartments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Department' }],
      createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check if Employee model exists
    const Employee = mongoose.models.Employee || mongoose.model('Employee', new mongoose.Schema({
      employeeId: String,
      firstName: String,
      lastName: String,
      email: String,
      departmentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Department' },
      positionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Position' },
      isActive: Boolean,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check if Department model exists
    const Department = mongoose.models.Department || mongoose.model('Department', new mongoose.Schema({
      name: String,
      code: String,
      description: String,
      isActive: Boolean,
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    }));

    // Check data counts
    const leaveCount = await Leave.countDocuments();
    const leaveTypeCount = await LeaveType.countDocuments();
    const employeeCount = await Employee.countDocuments();
    const departmentCount = await Department.countDocuments();

    console.log('\n📊 Database Statistics:');
    console.log(`   Leave Requests: ${leaveCount}`);
    console.log(`   Leave Types: ${leaveTypeCount}`);
    console.log(`   Employees: ${employeeCount}`);
    console.log(`   Departments: ${departmentCount}`);

    // If we have data, test the aggregation queries
    if (leaveCount > 0) {
      console.log('\n🔍 Testing aggregation queries...');
      
      // Test leave by status
      const statusResult = await Leave.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);
      console.log('   Leave by status:', statusResult);

      // Test leave by type
      const typeResult = await Leave.aggregate([
        {
          $group: {
            _id: '$leaveType',
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        }
      ]);
      console.log('   Leave by type:', typeResult);
    } else {
      console.log('\n⚠️  No leave requests found in database');
      console.log('   The reports page will show empty state');
    }

    console.log('\n✅ Test completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testLeaveReports();
