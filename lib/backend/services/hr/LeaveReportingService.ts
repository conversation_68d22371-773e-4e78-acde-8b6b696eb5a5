import { ReportingService } from '../base/ReportingService';
import { LeaveService, ILeave, Leave } from './LeaveService';
import logger, { LogCategory } from '@/lib/backend/utils/logger';
import { PdfGenerator } from '@/lib/backend/utils/pdf-generator';
import mongoose from 'mongoose';
import * as XLSX from 'xlsx';
import { Parser } from 'json2csv';

/**
 * Service for generating leave reports
 */
export class LeaveReportingService extends ReportingService<ILeave> {
  constructor() {
    super(new LeaveService(), 'Leave');
  }

  /**
   * Generate leave overview report with key statistics
   * @param options - Report options
   * @returns Overview report data
   */
  async generateOverviewReport(options: {
    startDate: Date;
    endDate: Date;
    departmentId?: string;
    timeRange: string;
  }): Promise<{
    overview: {
      totalLeaveDays: number;
      pendingRequests: number;
      approvalRate: number;
      avgResponseTime: number;
      previousPeriodComparison: {
        totalLeaveDaysChange: number;
        pendingRequestsChange: number;
        approvalRateChange: number;
        avgResponseTimeChange: number;
      };
    };
    leavesByType: Array<{ type: string; count: number; days: number; percentage: number }>;
    leavesByDepartment: Array<{ department: string; count: number; days: number; percentage: number }>;
    monthlyTrends: Array<{ month: string; count: number; days: number }>;
  }> {
    try {
      logger.info('Generating leave overview report', LogCategory.REPORTING, { options });

      // Build filter for current period
      const currentFilter: any = {
        $or: [
          {
            startDate: { $lte: options.endDate },
            endDate: { $gte: options.startDate }
          },
          {
            startDate: { $gte: options.startDate, $lte: options.endDate }
          },
          {
            endDate: { $gte: options.startDate, $lte: options.endDate }
          }
        ]
      };

      // Add department filter
      if (options.departmentId) {
        currentFilter.departmentId = new mongoose.Types.ObjectId(options.departmentId);
      }

      // Calculate previous period dates for comparison
      const periodDiff = options.endDate.getTime() - options.startDate.getTime();
      const prevStartDate = new Date(options.startDate.getTime() - periodDiff);
      const prevEndDate = new Date(options.startDate.getTime() - 1);

      const previousFilter = { ...currentFilter };
      previousFilter.$or = [
        {
          startDate: { $lte: prevEndDate },
          endDate: { $gte: prevStartDate }
        },
        {
          startDate: { $gte: prevStartDate, $lte: prevEndDate }
        },
        {
          endDate: { $gte: prevStartDate, $lte: prevEndDate }
        }
      ];

      // Get current period statistics
      const currentLeaves = await Leave.find(currentFilter);
      const previousLeaves = await Leave.find(previousFilter);

      // Calculate overview statistics
      const totalLeaveDays = currentLeaves
        .filter(leave => leave.status === 'approved')
        .reduce((sum, leave) => sum + leave.duration, 0);

      const pendingRequests = currentLeaves.filter(leave => leave.status === 'pending').length;

      const totalRequests = currentLeaves.length;
      const approvedRequests = currentLeaves.filter(leave => leave.status === 'approved').length;
      const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;

      // Calculate average response time for processed requests
      const processedRequests = currentLeaves.filter(leave =>
        (leave.status === 'approved' || leave.status === 'rejected') && leave.approvalDate
      );

      const avgResponseTime = processedRequests.length > 0
        ? processedRequests.reduce((sum, leave) => {
            const responseTime = (leave.approvalDate!.getTime() - leave.createdAt.getTime()) / (1000 * 60 * 60 * 24);
            return sum + responseTime;
          }, 0) / processedRequests.length
        : 0;

      // Calculate previous period statistics for comparison
      const prevTotalLeaveDays = previousLeaves
        .filter(leave => leave.status === 'approved')
        .reduce((sum, leave) => sum + leave.duration, 0);

      const prevPendingRequests = previousLeaves.filter(leave => leave.status === 'pending').length;

      const prevTotalRequests = previousLeaves.length;
      const prevApprovedRequests = previousLeaves.filter(leave => leave.status === 'approved').length;
      const prevApprovalRate = prevTotalRequests > 0 ? (prevApprovedRequests / prevTotalRequests) * 100 : 0;

      const prevProcessedRequests = previousLeaves.filter(leave =>
        (leave.status === 'approved' || leave.status === 'rejected') && leave.approvalDate
      );

      const prevAvgResponseTime = prevProcessedRequests.length > 0
        ? prevProcessedRequests.reduce((sum, leave) => {
            const responseTime = (leave.approvalDate!.getTime() - leave.createdAt.getTime()) / (1000 * 60 * 60 * 24);
            return sum + responseTime;
          }, 0) / prevProcessedRequests.length
        : 0;

      // Calculate percentage changes
      const totalLeaveDaysChange = prevTotalLeaveDays > 0
        ? ((totalLeaveDays - prevTotalLeaveDays) / prevTotalLeaveDays) * 100
        : 0;

      const pendingRequestsChange = prevPendingRequests > 0
        ? ((pendingRequests - prevPendingRequests) / prevPendingRequests) * 100
        : 0;

      const approvalRateChange = prevApprovalRate > 0
        ? ((approvalRate - prevApprovalRate) / prevApprovalRate) * 100
        : 0;

      const avgResponseTimeChange = prevAvgResponseTime > 0
        ? ((avgResponseTime - prevAvgResponseTime) / prevAvgResponseTime) * 100
        : 0;

      // Get leaves by type with percentages
      const typeResult = await Leave.aggregate([
        { $match: currentFilter },
        {
          $group: {
            _id: '$leaveType',
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const totalTypeRequests = typeResult.reduce((sum, item) => sum + item.count, 0);
      const leavesByType = typeResult.map(item => ({
        type: item._id,
        count: item.count,
        days: item.days,
        percentage: totalTypeRequests > 0 ? (item.count / totalTypeRequests) * 100 : 0
      }));

      // Get leaves by department with percentages
      const departmentResult = await Leave.aggregate([
        { $match: currentFilter },
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employee'
          }
        },
        { $unwind: { path: '$employee', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'departments',
            localField: 'employee.departmentId',
            foreignField: '_id',
            as: 'department'
          }
        },
        { $unwind: { path: '$department', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$department.name',
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const totalDeptRequests = departmentResult.reduce((sum, item) => sum + item.count, 0);
      const leavesByDepartment = departmentResult.map(item => ({
        department: item._id || 'No Department',
        count: item.count,
        days: item.days,
        percentage: totalDeptRequests > 0 ? (item.count / totalDeptRequests) * 100 : 0
      }));

      // Get monthly trends
      const monthResult = await Leave.aggregate([
        { $match: currentFilter },
        {
          $group: {
            _id: {
              year: { $year: '$startDate' },
              month: { $month: '$startDate' }
            },
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      const monthlyTrends = monthResult.map(item => {
        const date = new Date(item._id.year, item._id.month - 1, 1);
        return {
          month: date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }),
          count: item.count,
          days: item.days
        };
      });

      return {
        overview: {
          totalLeaveDays,
          pendingRequests,
          approvalRate: Math.round(approvalRate * 100) / 100,
          avgResponseTime: Math.round(avgResponseTime * 100) / 100,
          previousPeriodComparison: {
            totalLeaveDaysChange: Math.round(totalLeaveDaysChange * 100) / 100,
            pendingRequestsChange: Math.round(pendingRequestsChange * 100) / 100,
            approvalRateChange: Math.round(approvalRateChange * 100) / 100,
            avgResponseTimeChange: Math.round(avgResponseTimeChange * 100) / 100
          }
        },
        leavesByType,
        leavesByDepartment,
        monthlyTrends
      };
    } catch (error) {
      logger.error('Error generating leave overview report', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Generate leave summary report
   * @param options - Report options
   * @returns Report data
   */
  async generateSummaryReport(options: {
    startDate: Date;
    endDate: Date;
    departmentId?: string;
  }): Promise<{
    totalLeaves: number;
    approvedLeaves: number;
    pendingLeaves: number;
    rejectedLeaves: number;
    cancelledLeaves: number;
    leavesByType: Array<{ type: string; count: number; days: number }>;
    leavesByDepartment: Array<{ department: string; count: number; days: number }>;
    leavesByMonth: Array<{ month: string; count: number; days: number }>;
  }> {
    try {
      logger.info('Generating leave summary report', LogCategory.REPORTING, { options });
      
      // Build filter
      const filter: any = {
        $or: [
          {
            startDate: { $lte: options.endDate },
            endDate: { $gte: options.startDate }
          },
          {
            startDate: { $gte: options.startDate, $lte: options.endDate }
          },
          {
            endDate: { $gte: options.startDate, $lte: options.endDate }
          }
        ]
      };
      
      // Add department filter
      if (options.departmentId) {
        filter.departmentId = new mongoose.Types.ObjectId(options.departmentId);
      }
      
      // Get leave counts by status
      const statusResult = await Leave.aggregate([
        { $match: filter },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);
      
      const totalLeaves = statusResult.reduce((sum, item) => sum + item.count, 0);
      const approvedLeaves = statusResult.find(item => item._id === 'approved')?.count || 0;
      const pendingLeaves = statusResult.find(item => item._id === 'pending')?.count || 0;
      const rejectedLeaves = statusResult.find(item => item._id === 'rejected')?.count || 0;
      const cancelledLeaves = statusResult.find(item => item._id === 'cancelled')?.count || 0;
      
      // Get leaves by type
      const typeResult = await Leave.aggregate([
        { $match: filter },
        {
          $group: {
            _id: '$leaveType',
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      const leavesByType = typeResult.map(item => ({
        type: item._id,
        count: item.count,
        days: item.days
      }));
      
      // Get leaves by department
      const departmentResult = await Leave.aggregate([
        { $match: filter },
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeId',
            foreignField: '_id',
            as: 'employee'
          }
        },
        { $unwind: { path: '$employee', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'departments',
            localField: 'employee.departmentId',
            foreignField: '_id',
            as: 'department'
          }
        },
        { $unwind: { path: '$department', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$department.name',
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      const leavesByDepartment = departmentResult.map(item => ({
        department: item._id || 'No Department',
        count: item.count,
        days: item.days
      }));
      
      // Get leaves by month
      const monthResult = await Leave.aggregate([
        { $match: filter },
        {
          $group: {
            _id: {
              year: { $year: '$startDate' },
              month: { $month: '$startDate' }
            },
            count: { $sum: 1 },
            days: { $sum: '$duration' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);
      
      const leavesByMonth = monthResult.map(item => {
        const date = new Date(item._id.year, item._id.month - 1, 1);
        return {
          month: date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' }),
          count: item.count,
          days: item.days
        };
      });
      
      return {
        totalLeaves,
        approvedLeaves,
        pendingLeaves,
        rejectedLeaves,
        cancelledLeaves,
        leavesByType,
        leavesByDepartment,
        leavesByMonth
      };
    } catch (error) {
      logger.error('Error generating leave summary report', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Generate leave calendar report
   * @param options - Report options
   * @returns PDF buffer
   */
  async generateCalendarReport(options: {
    startDate: Date;
    endDate: Date;
    departmentId?: string;
    leaveType?: string;
  }): Promise<Buffer> {
    try {
      logger.info('Generating leave calendar report', LogCategory.REPORTING, { options });
      
      // Build filter
      const filter: any = {
        $or: [
          {
            startDate: { $lte: options.endDate },
            endDate: { $gte: options.startDate }
          },
          {
            startDate: { $gte: options.startDate, $lte: options.endDate }
          },
          {
            endDate: { $gte: options.startDate, $lte: options.endDate }
          }
        ],
        status: 'approved'
      };
      
      // Add leave type filter
      if (options.leaveType) {
        filter.leaveType = options.leaveType;
      }
      
      // Get leave requests
      const leaveService = new LeaveService();
      const leaves = await leaveService.find(
        filter,
        { sort: { startDate: 1 } },
        ['employeeId']
      );
      
      if (leaves.length === 0) {
        throw new Error('No approved leave requests found for calendar report');
      }
      
      // Create PDF document
      const pdfGenerator = new PdfGenerator({
        title: 'Leave Calendar',
        subtitle: `${options.startDate.toLocaleDateString()} to ${options.endDate.toLocaleDateString()}`,
        pageSize: 'A4',
        pageOrientation: 'landscape',
        margins: { top: 50, bottom: 50, left: 50, right: 50 }
      });
      
      // Add header
      pdfGenerator.addHeader({
        showTitle: true,
        showDate: true
      });
      
      // Add footer
      pdfGenerator.addFooter({
        showPageNumber: true
      });
      
      // Add title
      pdfGenerator.addTitle('Leave Calendar', { align: 'center' });
      
      // Group leaves by department
      const departmentMap = new Map<string, ILeave[]>();
      
      leaves.forEach(leave => {
        const employee = leave.employeeId as any;
        const department = employee && employee.departmentId ? (employee.departmentId as any).name : 'No Department';
        
        if (!departmentMap.has(department)) {
          departmentMap.set(department, []);
        }
        
        departmentMap.get(department)!.push(leave);
      });
      
      // Add leaves by department
      for (const [department, departmentLeaves] of departmentMap.entries()) {
        // Add department title
        pdfGenerator.addTitle(department, { fontSize: 14, align: 'left' });
        
        // Define table columns
        const columns = [
          { header: 'Employee', property: 'employee', width: 150 },
          { header: 'Leave Type', property: 'leaveType', width: 100 },
          { header: 'Start Date', property: 'startDate', width: 100 },
          { header: 'End Date', property: 'endDate', width: 100 },
          { header: 'Duration', property: 'duration', width: 80, align: 'right' as const },
          { header: 'Reason', property: 'reason', width: 200 }
        ];
        
        // Prepare table data
        const tableData = departmentLeaves.map(leave => {
          const employee = leave.employeeId as any;
          
          return {
            employee: employee ? `${employee.firstName} ${employee.lastName}` : '',
            leaveType: leave.leaveType,
            startDate: leave.startDate.toLocaleDateString(),
            endDate: leave.endDate.toLocaleDateString(),
            duration: `${leave.duration} days`,
            reason: leave.reason || ''
          };
        });
        
        // Add table
        pdfGenerator.addTable(tableData, columns, {
          headerBgColor: '#f2f2f2',
          alternateRowBgColor: '#f9f9f9'
        });
      }
      
      // Generate PDF
      return await pdfGenerator.generate();
    } catch (error) {
      logger.error('Error generating leave calendar report', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Generate employee leave report
   * @param employeeId - Employee ID
   * @param options - Report options
   * @returns Report data
   */
  async generateEmployeeLeaveReport(
    employeeId: string,
    options: {
      year: number;
    }
  ): Promise<{
    employee: {
      id: string;
      name: string;
      department: string;
      position: string;
    };
    summary: {
      totalLeaves: number;
      totalDays: number;
      approvedLeaves: number;
      approvedDays: number;
      pendingLeaves: number;
      pendingDays: number;
      rejectedLeaves: number;
      rejectedDays: number;
    };
    leavesByType: Array<{
      type: string;
      count: number;
      days: number;
    }>;
    leaveHistory: Array<{
      leaveId: string;
      leaveType: string;
      startDate: string;
      endDate: string;
      duration: number;
      status: string;
      reason?: string;
    }>;
  }> {
    try {
      logger.info('Generating employee leave report', LogCategory.REPORTING, { employeeId, options });
      
      // Build filter
      const filter: any = {
        employeeId: new mongoose.Types.ObjectId(employeeId),
        $or: [
          { startDate: { $gte: new Date(options.year, 0, 1), $lte: new Date(options.year, 11, 31) } },
          { endDate: { $gte: new Date(options.year, 0, 1), $lte: new Date(options.year, 11, 31) } }
        ]
      };
      
      // Get employee details
      const Employee = mongoose.models.Employee;
      if (!Employee) {
        throw new Error('Employee model not found');
      }
      
      const employee = await Employee.findById(employeeId)
        .populate('departmentId')
        .populate('positionId')
        .lean();
      
      if (!employee) {
        throw new Error(`Employee with ID ${employeeId} not found`);
      }
      
      // Get leave requests
      const leaveService = new LeaveService();
      const leaves = await leaveService.find(
        filter,
        { sort: { startDate: -1 } }
      );
      
      // Calculate summary
      const totalLeaves = leaves.length;
      const totalDays = leaves.reduce((sum, leave) => sum + leave.duration, 0);
      
      const approvedLeaves = leaves.filter(leave => leave.status === 'approved').length;
      const approvedDays = leaves
        .filter(leave => leave.status === 'approved')
        .reduce((sum, leave) => sum + leave.duration, 0);
      
      const pendingLeaves = leaves.filter(leave => leave.status === 'pending').length;
      const pendingDays = leaves
        .filter(leave => leave.status === 'pending')
        .reduce((sum, leave) => sum + leave.duration, 0);
      
      const rejectedLeaves = leaves.filter(leave => leave.status === 'rejected').length;
      const rejectedDays = leaves
        .filter(leave => leave.status === 'rejected')
        .reduce((sum, leave) => sum + leave.duration, 0);
      
      // Calculate leaves by type
      const leaveTypeMap = new Map<string, { count: number; days: number }>();
      
      leaves.forEach(leave => {
        if (!leaveTypeMap.has(leave.leaveType)) {
          leaveTypeMap.set(leave.leaveType, { count: 0, days: 0 });
        }
        
        const typeStats = leaveTypeMap.get(leave.leaveType)!;
        typeStats.count++;
        typeStats.days += leave.duration;
      });
      
      const leavesByType = Array.from(leaveTypeMap.entries()).map(([type, stats]) => ({
        type,
        count: stats.count,
        days: stats.days
      }));
      
      // Prepare leave history
      const leaveHistory = leaves.map(leave => ({
        leaveId: leave.leaveId,
        leaveType: leave.leaveType,
        startDate: leave.startDate.toLocaleDateString(),
        endDate: leave.endDate.toLocaleDateString(),
        duration: leave.duration,
        status: leave.status,
        reason: leave.reason
      }));
      
      return {
        employee: {
          id: employee.employeeId,
          name: `${employee.firstName} ${employee.lastName}`,
          department: employee.departmentId ? employee.departmentId.name : 'No Department',
          position: employee.positionId ? employee.positionId.name : 'No Position'
        },
        summary: {
          totalLeaves,
          totalDays,
          approvedLeaves,
          approvedDays,
          pendingLeaves,
          pendingDays,
          rejectedLeaves,
          rejectedDays
        },
        leavesByType,
        leaveHistory
      };
    } catch (error) {
      logger.error('Error generating employee leave report', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Export report data to CSV format
   * @param data - Report data
   * @param reportType - Type of report
   * @returns CSV content as string
   */
  async exportToCSV(data: any, reportType: string): Promise<string> {
    try {
      logger.info('Exporting leave report to CSV', LogCategory.REPORTING, { reportType });

      let csvData: any[] = [];
      let fields: string[] = [];

      switch (reportType) {
        case 'overview':
          // Flatten overview data for CSV export
          csvData = [
            {
              metric: 'Total Leave Days',
              value: data.overview.totalLeaveDays,
              change: `${data.overview.previousPeriodComparison.totalLeaveDaysChange}%`
            },
            {
              metric: 'Pending Requests',
              value: data.overview.pendingRequests,
              change: `${data.overview.previousPeriodComparison.pendingRequestsChange}%`
            },
            {
              metric: 'Approval Rate',
              value: `${data.overview.approvalRate}%`,
              change: `${data.overview.previousPeriodComparison.approvalRateChange}%`
            },
            {
              metric: 'Avg Response Time',
              value: `${data.overview.avgResponseTime} days`,
              change: `${data.overview.previousPeriodComparison.avgResponseTimeChange}%`
            }
          ];

          // Add leave by type data
          data.leavesByType.forEach((item: any) => {
            csvData.push({
              metric: `Leave Type - ${item.type}`,
              value: `${item.count} requests (${item.days} days)`,
              change: `${item.percentage.toFixed(1)}% of total`
            });
          });

          // Add leave by department data
          data.leavesByDepartment.forEach((item: any) => {
            csvData.push({
              metric: `Department - ${item.department}`,
              value: `${item.count} requests (${item.days} days)`,
              change: `${item.percentage.toFixed(1)}% of total`
            });
          });

          fields = ['metric', 'value', 'change'];
          break;

        case 'utilization':
          csvData = [
            {
              metric: 'Total Employees',
              value: data.totalEmployees
            },
            {
              metric: 'Total Leave Requests',
              value: data.totalLeaveRequests
            },
            {
              metric: 'Approved Requests',
              value: data.approvedRequests
            },
            {
              metric: 'Rejected Requests',
              value: data.rejectedRequests
            },
            {
              metric: 'Pending Requests',
              value: data.pendingRequests
            },
            {
              metric: 'Total Leave Days Taken',
              value: data.totalLeaveDaysTaken
            },
            {
              metric: 'Average Leave Days Per Employee',
              value: data.averageLeaveDaysPerEmployee
            }
          ];

          // Add utilization by leave type
          data.utilizationByLeaveType.forEach((item: any) => {
            csvData.push({
              metric: `Leave Type - ${item.leaveType}`,
              value: `${item.totalRequests} requests (${item.totalDays} days)`
            });
          });

          fields = ['metric', 'value'];
          break;

        case 'department':
          csvData = data.departments || [];
          fields = Object.keys(csvData[0] || {});
          break;

        default:
          throw new Error(`Unsupported report type for CSV export: ${reportType}`);
      }

      if (csvData.length === 0) {
        return 'No data available for export';
      }

      const parser = new Parser({ fields });
      return parser.parse(csvData);

    } catch (error) {
      logger.error('Error exporting leave report to CSV', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Export report data to Excel format
   * @param data - Report data
   * @param reportType - Type of report
   * @returns Excel buffer
   */
  async exportToExcel(data: any, reportType: string): Promise<Buffer> {
    try {
      logger.info('Exporting leave report to Excel', LogCategory.REPORTING, { reportType });

      const workbook = XLSX.utils.book_new();

      switch (reportType) {
        case 'overview':
          // Overview sheet
          const overviewData = [
            ['Metric', 'Value', 'Change from Previous Period'],
            ['Total Leave Days', data.overview.totalLeaveDays, `${data.overview.previousPeriodComparison.totalLeaveDaysChange}%`],
            ['Pending Requests', data.overview.pendingRequests, `${data.overview.previousPeriodComparison.pendingRequestsChange}%`],
            ['Approval Rate', `${data.overview.approvalRate}%`, `${data.overview.previousPeriodComparison.approvalRateChange}%`],
            ['Avg Response Time', `${data.overview.avgResponseTime} days`, `${data.overview.previousPeriodComparison.avgResponseTimeChange}%`]
          ];

          const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
          XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Overview');

          // Leave by Type sheet
          if (data.leavesByType && data.leavesByType.length > 0) {
            const typeData = [
              ['Leave Type', 'Requests', 'Days', 'Percentage'],
              ...data.leavesByType.map((item: any) => [
                item.type,
                item.count,
                item.days,
                `${item.percentage.toFixed(1)}%`
              ])
            ];

            const typeSheet = XLSX.utils.aoa_to_sheet(typeData);
            XLSX.utils.book_append_sheet(workbook, typeSheet, 'By Type');
          }

          // Leave by Department sheet
          if (data.leavesByDepartment && data.leavesByDepartment.length > 0) {
            const deptData = [
              ['Department', 'Requests', 'Days', 'Percentage'],
              ...data.leavesByDepartment.map((item: any) => [
                item.department,
                item.count,
                item.days,
                `${item.percentage.toFixed(1)}%`
              ])
            ];

            const deptSheet = XLSX.utils.aoa_to_sheet(deptData);
            XLSX.utils.book_append_sheet(workbook, deptSheet, 'By Department');
          }

          // Monthly Trends sheet
          if (data.monthlyTrends && data.monthlyTrends.length > 0) {
            const trendsData = [
              ['Month', 'Requests', 'Days'],
              ...data.monthlyTrends.map((item: any) => [
                item.month,
                item.count,
                item.days
              ])
            ];

            const trendsSheet = XLSX.utils.aoa_to_sheet(trendsData);
            XLSX.utils.book_append_sheet(workbook, trendsSheet, 'Monthly Trends');
          }
          break;

        default:
          // Generic export for other report types
          const genericSheet = XLSX.utils.json_to_sheet([data]);
          XLSX.utils.book_append_sheet(workbook, genericSheet, 'Report Data');
          break;
      }

      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    } catch (error) {
      logger.error('Error exporting leave report to Excel', LogCategory.REPORTING, error);
      throw error;
    }
  }

  /**
   * Export report data to PDF format
   * @param data - Report data
   * @param reportType - Type of report
   * @returns PDF buffer
   */
  async exportToPDF(data: any, reportType: string): Promise<Buffer> {
    try {
      logger.info('Exporting leave report to PDF', LogCategory.REPORTING, { reportType });

      const pdfGenerator = new PdfGenerator({
        title: `Leave ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
        subtitle: `Generated on ${new Date().toLocaleDateString()}`,
        pageSize: 'A4',
        pageOrientation: 'portrait',
        margins: { top: 50, bottom: 50, left: 50, right: 50 }
      });

      // Add header
      pdfGenerator.addHeader({
        showTitle: true,
        showDate: true
      });

      // Add footer
      pdfGenerator.addFooter({
        showPageNumber: true
      });

      // Add title
      pdfGenerator.addTitle(`Leave ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`, { align: 'center' });

      switch (reportType) {
        case 'overview':
          // Overview statistics
          pdfGenerator.addTitle('Overview Statistics', { fontSize: 14, align: 'left' });

          const overviewData = [
            ['Metric', 'Value', 'Change'],
            ['Total Leave Days', data.overview.totalLeaveDays.toString(), `${data.overview.previousPeriodComparison.totalLeaveDaysChange > 0 ? '+' : ''}${data.overview.previousPeriodComparison.totalLeaveDaysChange}%`],
            ['Pending Requests', data.overview.pendingRequests.toString(), `${data.overview.previousPeriodComparison.pendingRequestsChange > 0 ? '+' : ''}${data.overview.previousPeriodComparison.pendingRequestsChange}%`],
            ['Approval Rate', `${data.overview.approvalRate}%`, `${data.overview.previousPeriodComparison.approvalRateChange > 0 ? '+' : ''}${data.overview.previousPeriodComparison.approvalRateChange}%`],
            ['Avg Response Time', `${data.overview.avgResponseTime} days`, `${data.overview.previousPeriodComparison.avgResponseTimeChange > 0 ? '+' : ''}${data.overview.previousPeriodComparison.avgResponseTimeChange}%`]
          ];

          pdfGenerator.addTable(overviewData, [
            { header: 'Metric', property: 'metric', width: 200 },
            { header: 'Value', property: 'value', width: 150, align: 'right' },
            { header: 'Change', property: 'change', width: 100, align: 'right' }
          ], {
            headerBgColor: '#f2f2f2',
            alternateRowBgColor: '#f9f9f9'
          });

          // Leave by Type
          if (data.leavesByType && data.leavesByType.length > 0) {
            pdfGenerator.addTitle('Leave by Type', { fontSize: 14, align: 'left' });

            const typeTableData = data.leavesByType.map((item: any) => [
              item.type,
              item.count.toString(),
              item.days.toString(),
              `${item.percentage.toFixed(1)}%`
            ]);

            pdfGenerator.addTable([['Leave Type', 'Requests', 'Days', 'Percentage'], ...typeTableData], [
              { header: 'Leave Type', property: 'type', width: 150 },
              { header: 'Requests', property: 'requests', width: 100, align: 'right' },
              { header: 'Days', property: 'days', width: 100, align: 'right' },
              { header: 'Percentage', property: 'percentage', width: 100, align: 'right' }
            ], {
              headerBgColor: '#f2f2f2',
              alternateRowBgColor: '#f9f9f9'
            });
          }

          // Leave by Department
          if (data.leavesByDepartment && data.leavesByDepartment.length > 0) {
            pdfGenerator.addTitle('Leave by Department', { fontSize: 14, align: 'left' });

            const deptTableData = data.leavesByDepartment.map((item: any) => [
              item.department,
              item.count.toString(),
              item.days.toString(),
              `${item.percentage.toFixed(1)}%`
            ]);

            pdfGenerator.addTable([['Department', 'Requests', 'Days', 'Percentage'], ...deptTableData], [
              { header: 'Department', property: 'department', width: 150 },
              { header: 'Requests', property: 'requests', width: 100, align: 'right' },
              { header: 'Days', property: 'days', width: 100, align: 'right' },
              { header: 'Percentage', property: 'percentage', width: 100, align: 'right' }
            ], {
              headerBgColor: '#f2f2f2',
              alternateRowBgColor: '#f9f9f9'
            });
          }
          break;

        default:
          pdfGenerator.addText('Report data not available for PDF export.');
          break;
      }

      return await pdfGenerator.generate();

    } catch (error) {
      logger.error('Error exporting leave report to PDF', LogCategory.REPORTING, error);
      throw error;
    }
  }
}
